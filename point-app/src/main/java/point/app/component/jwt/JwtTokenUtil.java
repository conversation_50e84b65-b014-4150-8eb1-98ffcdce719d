package point.app.component.jwt;

import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationServiceException;
import point.common.component.RedisManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;

@Slf4j
public class JwtTokenUtil {

    public static final String HEADER_PREFIX = "Bearer ";
    public static final String HEADER_NAME = "X-Authorization";

    public static Map<String, Claim> getClaim(JwtManager jwtManager, String token) {
        DecodedJWT decodedJWT = jwtManager.decodedJWT(token);
        if (decodedJWT == null) {
            log.info("jwt token cannot parse: {}", token);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        Map<String, Claim> claims = decodedJWT.getClaims();
        if (!jwtManager.isAccessToken(claims)) {
            log.info("jwt token scope invalid");
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }
        return claims;
    }

    public static boolean isAccessToken(JwtManager jwtManager, Map<String, Claim> claims) {
        return jwtManager.isAccessToken(claims);
    }

    public static String getJwtFromRedis(RedisManager redisManager, Long userId) {
        String key = CommonConstants.REDIS_USER_TOCKN_PREFIX + userId;
        return redisManager.hget(key, JwtManager.TOKEN);
    }

    public static String extractToken(String tokenPayload) {
        if (StringUtils.isBlank(tokenPayload)) {
            log.info("no {} header or value is empty", HEADER_NAME);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        if (tokenPayload.length() < HEADER_PREFIX.length()) {
            log.info("the jwt token cannot found in request header {}", HEADER_NAME);
            throw new AuthenticationServiceException(
                    Integer.toString(ErrorCode.REQUEST_ERROR_JWT_TOKEN_INVALIDATE.getCode()));
        }

        return tokenPayload.substring(HEADER_PREFIX.length());
    }
}
