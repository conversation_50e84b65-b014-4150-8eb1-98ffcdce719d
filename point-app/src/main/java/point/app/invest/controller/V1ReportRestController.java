package point.app.invest.controller;

import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.ibm.icu.util.Calendar;
import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.v3.oas.annotations.Hidden;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import point.app.component.model.UserPrincipal;
import point.common.component.CsvDownloadManager;
import point.common.component.RedisManager;
import point.common.component.S3Manager;
import point.common.component.S3Manager.Bucket;
import point.common.config.S3Config;
import point.common.constant.ErrorCode;
import point.common.constant.KycStatus;
import point.common.entity.User;
import point.common.entity.UserKyc;
import point.common.entity.YearlyReportCreateInfo;
import point.common.exception.CustomException;
import point.common.model.response.ReportData;
import point.common.model.response.ReportText;
import point.common.service.ReportService;
import point.common.service.UserKycService;
import point.common.service.UserService;
import point.common.service.YearlyReportCreateInfoService;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;

@Hidden
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/report")
public class V1ReportRestController {

    private final ReportService reportService;
    private final TemplateEngine templateEngine;
    private final CsvDownloadManager<ReportText> downloadManager;
    private final S3Config s3Config;
    private final S3Manager s3Manager;
    private final YearlyReportCreateInfoService yearlyReportCreateInfoService;
    private final RedisManager redisManager;
    private final UserService userService;
    private final UserKycService userKycService;

    private static final String DAIRY_REPORT_TEMPLATE = "report/daily.txt";

    @GetMapping("/daily/download")
    public String downloadDaily(
            HttpServletResponse response,
            @AuthenticationPrincipal UserPrincipal user,
            @Valid @Pattern(regexp = "^\\d{8}$") @RequestParam(value = "firstDay", required = true)
                    String firstDay,
            @Pattern(regexp = "^\\d{8}$") @RequestParam(value = "lastDay", required = true)
                    String lastDay)
            throws Exception {

        ReportData reportData = reportService.createReportData(user.getId(), firstDay, lastDay);
        // Create csv
        final Context ctx = new Context(Locale.getDefault());
        ctx.setVariable("report", reportData);
        ctx.setVariable("isDayly", true); // 対象期間の表示変更
        String reportText = this.templateEngine.process(DAIRY_REPORT_TEMPLATE, ctx);

        String fileNamePrefix = "dairy_report";
        downloadManager.download(response, reportText, fileNamePrefix);

        return null;
    }

    @GetMapping("/monthly/download")
    public String downloadMonthly(
            HttpServletResponse response,
            @AuthenticationPrincipal UserPrincipal user,
            @Valid
                    @Pattern(regexp = "^\\d{6}$")
                    @RequestParam(value = "targetMonth", required = true)
                    String targetMonth)
            throws Exception {
        String firstDay = targetMonth + "01";
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, Integer.parseInt(targetMonth.substring(0, 4)));
        calendar.set(
                Calendar.MONTH, Integer.parseInt(targetMonth.substring(4, 6)) - 1); // MONTHは0〜11
        String lastDayOfMonth = String.format("%02d", calendar.getActualMaximum(Calendar.DATE));
        String lastDay = targetMonth + lastDayOfMonth;
        ReportData reportData = reportService.createReportData(user.getId(), firstDay, lastDay);
        // Create csv
        final Context ctx = new Context(Locale.getDefault());
        ctx.setVariable("report", reportData);
        ctx.setVariable("isMonthly", true); // 対象期間の表示変更
        String reportText = this.templateEngine.process(DAIRY_REPORT_TEMPLATE, ctx);

        String fileNamePrefix = "monthly_report";
        downloadManager.download(response, reportText, fileNamePrefix);
        return null;
    }

    @GetMapping("/yearly/download")
    public String downloadYearly(
            @AuthenticationPrincipal UserPrincipal user,
            @Valid
                    @Pattern(regexp = "^\\d{4}$")
                    @RequestParam(value = "targetYear", required = true)
                    String targetYear,
            HttpServletResponse response)
            throws Exception {
        Bucket bucket = new Bucket(s3Config.getYearReportBucket().getName());
        OutputStream outputStream = response.getOutputStream();
        List<S3ObjectSummary> s3ObjectSummaryList = null;
        try {
            s3ObjectSummaryList =
                    s3Manager.listObjects(
                            bucket,
                            targetYear
                                    + "/yearlyReport_"
                                    + user.getId()
                                    + "_"
                                    + targetYear
                                    + ".pdf");
            log.info("" + s3ObjectSummaryList);
        } catch (Exception e) {
            log.warn(
                    "yearly report s3 connect failed.userid:{} targetyear:{} {}",
                    user.getId(),
                    targetYear,
                    e.getMessage());
            throw new CustomException(ErrorCode.REQUEST_ERROR_YEARLY_REPORT_DOWNLOAD_FAILED);
        }
        if (ObjectUtils.isNotEmpty(s3ObjectSummaryList)) {
            S3ObjectSummary s3ObjectSummary = s3ObjectSummaryList.get(0);
            if (s3ObjectSummary != null) {
                File tmpFile = null;
                FileOutputStream fos = null;
                try {
                    tmpFile = new File(s3ObjectSummary.getKey());
                    fos = FileUtils.openOutputStream(tmpFile);
                    // get pdf from s3
                    IOUtils.copy(
                            s3Manager.getObjectContentWithoutSeed(bucket, s3ObjectSummary.getKey()),
                            fos);
                    // file to base64
                    byte[] data = Files.readAllBytes(tmpFile.toPath());
                    outputStream.write(data);
                    response.setContentType("application/pdf");
                    response.setCharacterEncoding("utf-8");
                    response.setHeader("Content-Disposition", "attachment;filename=report.pdf");
                    List<YearlyReportCreateInfo> createInfo =
                            yearlyReportCreateInfoService.findByCondition(
                                    Integer.valueOf(targetYear), null, user.getId());
                    if (ObjectUtils.isNotEmpty(createInfo)) {
                        YearlyReportCreateInfo createInfoForUpdate =
                                createInfo.get(createInfo.size() - 1);
                        createInfoForUpdate.setUpdatedBy(user.getId().toString());
                        createInfoForUpdate.setDownloadFlg(true);
                        createInfoForUpdate.setLatestDownloadTime(new Date());
                        createInfoForUpdate.setS3FileName(s3ObjectSummary.getKey());
                        yearlyReportCreateInfoService.save(createInfoForUpdate);
                    }
                } catch (Exception e) {
                    log.warn(
                            "yearly report download failed.userid:{} targetyear:{} {}",
                            user.getId(),
                            targetYear,
                            e.getMessage());
                    throw new CustomException(
                            ErrorCode.REQUEST_ERROR_YEARLY_REPORT_DOWNLOAD_FAILED);
                } finally {
                    IOUtils.closeQuietly(fos);
                    FileUtils.deleteQuietly(tmpFile);
                }
            } else {
                throw new CustomException(ErrorCode.REQUEST_ERROR_YEARLY_REPORT_DOWNLOAD_FAILED);
            }
        } else {
            throw new CustomException(ErrorCode.REQUEST_ERROR_YEARLY_REPORT_DOWNLOAD_FAILED);
        }
        return null;
    }

    @GetMapping("/yearly/created")
    public ResponseEntity<Object> get(@AuthenticationPrincipal UserPrincipal user) {
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        // UTC->JST 年度計算
        if (currentMonth == 12 && day == 31 && hour >= 15) {
            currentYear = currentYear + 1;
        }
        List<Integer> createdYearList = new ArrayList<Integer>();
        int startYear =
                yearlyReportCreateInfoService.findAll().stream()
                        .map(YearlyReportCreateInfo::getYear)
                        .min(Integer::compareTo)
                        .orElse(2023);
        int endYear =
                yearlyReportCreateInfoService.findAll().stream()
                        .map(YearlyReportCreateInfo::getYear)
                        .max(Integer::compareTo)
                        .orElse(currentYear - 1);
        for (int year = startYear; year <= endYear; year++) {
            String targetYearCreated = redisManager.get("yearly_report_created:" + year);
            if (StringUtils.isNotEmpty(targetYearCreated)) {
                List<YearlyReportCreateInfo> userHaveReport =
                        yearlyReportCreateInfoService.findByCondition(
                                year, "SUCCESS", user.getId());
                if (ObjectUtils.isNotEmpty(userHaveReport)) {
                    createdYearList.add(year);
                }
            } else {
                String createdToString = year + "1231" + "150000";
                Date createdTo = FormatUtil.parse(createdToString, FormatPattern.YYYYMMDDHHMMSS);
                List<User> userList = userService.findByCreatedAt(createdTo.getTime());
                List<YearlyReportCreateInfo> createdUser =
                        yearlyReportCreateInfoService.findByCondition(year, "SUCCESS", null);
                List<Long> uncreatedUserList =
                        userList.stream()
                                .map(User::getId)
                                .filter(
                                        u ->
                                                createCheck(
                                                        userKycService
                                                                .findByUserId(u)
                                                                .orElse(new ArrayList<>()),
                                                        createdTo))
                                .filter(
                                        u ->
                                                !createdUser.stream()
                                                        .map(YearlyReportCreateInfo::getUserId)
                                                        .toList()
                                                        .contains(u))
                                .collect(Collectors.toList());
                if (ObjectUtils.isEmpty(uncreatedUserList)) {
                    redisManager.set("yearly_report_created:" + year, "DONE");
                    List<YearlyReportCreateInfo> userHaveReport =
                            yearlyReportCreateInfoService.findByCondition(
                                    year, "SUCCESS", user.getId());
                    if (ObjectUtils.isNotEmpty(userHaveReport)) {
                        createdYearList.add(year);
                    }
                }
            }
        }
        return ResponseEntity.ok(createdYearList);
    }

    private boolean createCheck(List<UserKyc> userKycList, Date createdTo) {
        if (userKycList.stream()
                .anyMatch(
                        s ->
                                s.getCreatedAt()
                                                        .compareTo(
                                                                new Date(
                                                                        createdTo.getTime()
                                                                                - 1000 * 60 * 60
                                                                                        * 24
                                                                                        * 365l))
                                                < 0
                                        && (s.getKycStatus() == KycStatus.ACCOUNT_CLOSED))) {
            return false;
        } else {
            if (userKycList.stream()
                    .anyMatch(
                            s ->
                                    s.getCreatedAt().compareTo(createdTo) < 0
                                            && (s.getKycStatus()
                                                    == KycStatus.ACCOUNT_OPENING_DONE))) {
                return true;
            } else {
                return false;
            }
        }
    }
}
