package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** VaIssueResponse */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class VaIssueResponse {
    @JsonAlias("vaTypeCode")
    private String vaTypeCode;

    @JsonAlias("vaTypeName")
    private String vaTypeName;

    @JsonAlias("expireDateTime")
    private String expireDateTime;

    @JsonAlias("vaHolderNameKana")
    private String vaHolderNameKana;

    @JsonAlias("vaList")
    private List<Va> vaList;

    private String errorCode;

    private String errorMessage;

    public VaIssueResponse vaTypeCode(String vaTypeCode) {
        this.vaTypeCode = vaTypeCode;
        return this;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class VaIssueResponse {\n");

        sb.append("    vaTypeCode: ").append(toIndentedString(vaTypeCode)).append("\n");
        sb.append("    vaTypeName: ").append(toIndentedString(vaTypeName)).append("\n");
        sb.append("    expireDateTime: ").append(toIndentedString(expireDateTime)).append("\n");
        sb.append("    vaHolderNameKana: ").append(toIndentedString(vaHolderNameKana)).append("\n");
        sb.append("    vaList: ").append(toIndentedString(vaList)).append("\n");
        sb.append("    errorCode: ").append(toIndentedString(errorCode)).append("\n");
        sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces (except the first
     * line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
