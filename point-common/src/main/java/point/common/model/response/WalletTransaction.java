package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WalletTransaction<T> implements Serializable {

    private static final long serialVersionUID = 5156912381132710960L;

    @Getter
    @Setter
    @JsonProperty(value = "id")
    private Long id;

    @Getter
    @Setter
    @JsonProperty(value = "status")
    private int status;

    @Getter
    @Setter
    @JsonProperty(value = "symbol")
    private String symbol;

    @Getter
    @Setter
    @JsonProperty(value = "txAmount")
    private String txAmount;

    @Getter
    @Setter
    @JsonProperty(value = "txFee")
    private String txFee;

    @Getter
    @Setter
    @JsonProperty(value = "txHash")
    private String txHash;

    @Getter
    @Setter
    @JsonProperty(value = "txFrom")
    private String txFrom;

    @Getter
    @Setter
    @JsonProperty(value = "txTo")
    private String txTo;

    @Getter
    @Setter
    @JsonProperty(value = "note")
    private String note;

    @Getter
    @Setter
    @JsonProperty(value = "createTime")
    private Long createTime;

    @Getter
    @Setter
    @JsonProperty(value = "tag")
    private String tag;
}
