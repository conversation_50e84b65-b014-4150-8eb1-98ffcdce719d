package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import point.common.constant.ErrorCode;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErrorData implements Serializable {

    private static final long serialVersionUID = -7585866804235886483L;

    @Getter
    @Setter
    @JsonProperty("code")
    private int code;

    @Getter
    @Setter
    @JsonProperty("message")
    private String message;

    @Getter
    @Setter
    @JsonProperty("params")
    private List<Object> params;

    public ErrorData(ErrorCode errorCode) {
        code = errorCode.getCode();
    }

    public ErrorData(ErrorCode errorCode, String message) {
        code = errorCode.getCode();
        this.message = message;
    }

    public ErrorData(ErrorCode errorCode, List<Object> params) {
        this.code = errorCode.getCode();
        this.params = params;
    }
}
