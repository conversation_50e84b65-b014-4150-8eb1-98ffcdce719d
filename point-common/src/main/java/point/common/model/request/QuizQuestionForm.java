package point.common.model.request;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import point.common.entity.QuizQuestion;

@Getter
@Setter
public class QuizQuestionForm {

    private Long id;
    private String quizNum;
    private String title;
    private String optionA;
    private String optionB;
    private String optionC;
    private String optionD;

    private String optionAWrongReason;
    private String optionBWrongReason;
    private String optionCWrongReason;
    private String optionDWrongReason;
    private String answer;
    private String correctAnswerContent;
    private Date publishedAt;

    public QuizQuestion initQuizQuestion() {
        QuizQuestion quizQuestion = new QuizQuestion();
        quizQuestion.setQuizNum(quizNum);
        quizQuestion.setTitle(title);
        quizQuestion.setOptionA(optionA);
        quizQuestion.setOptionB(optionB);
        quizQuestion.setOptionC(optionC);
        quizQuestion.setOptionD(optionD);
        quizQuestion.setOptionAWrongReason(optionAWrongReason);
        quizQuestion.setOptionBWrongReason(optionBWrongReason);
        quizQuestion.setOptionCWrongReason(optionCWrongReason);
        quizQuestion.setOptionDWrongReason(optionDWrongReason);
        quizQuestion.setAnswer(answer);
        quizQuestion.setCorrectAnswerContent(correctAnswerContent);
        quizQuestion.setPublishedAt(publishedAt);
        quizQuestion.setEnable(true);
        return quizQuestion;
    }

    public QuizQuestion initExistQuizQuestion() {
        QuizQuestion quizQuestion = this.initQuizQuestion();
        quizQuestion.setId(id);
        return quizQuestion;
    }
}
