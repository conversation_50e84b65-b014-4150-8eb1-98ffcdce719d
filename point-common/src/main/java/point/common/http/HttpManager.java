package point.common.http;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import point.common.component.CustomLogger;
import point.common.constant.ErrorCode;
import point.common.exception.HttpException;
import point.common.model.response.SNSResponse;
import point.common.util.CharsetUtil;
import point.common.util.DateUnit;
import point.common.util.JsonUtil;

@Component
public class HttpManager<S extends Serializable> {

    private static final CustomLogger log = new CustomLogger(HttpManager.class.getName());

    public String createUri(String host, String path) {
        return host + path;
    }

    private CloseableHttpResponse getResponse(
            HttpRequestBase request, CloseableHttpClient httpClient) throws Exception {
        return httpClient.execute(request);
    }

    @SuppressWarnings("unchecked")
    private S doRequest(
            HttpRequestBase request,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            int timeoutMillis = (int) DateUnit.SECOND.getMillis() * 20;
            request.setConfig(
                    RequestConfig.custom()
                            .setConnectionRequestTimeout(timeoutMillis)
                            .setConnectTimeout(timeoutMillis)
                            .build());

            if (!CollectionUtils.isEmpty(headerMap)) {
                headerMap
                        .entrySet()
                        .forEach(entry -> request.addHeader(entry.getKey(), entry.getValue()));
            }

            try (CloseableHttpResponse response = getResponse(request, httpClient)) {
                String entity =
                        EntityUtils.toString(response.getEntity(), CharsetUtil.UTF_8.getCharset());

                if (response.getStatusLine() == null) {
                    throw new HttpException(
                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR, "status line is null");
                }

                if (!(response.getStatusLine().getStatusCode() == HttpStatus.SC_OK
                        || (clazz.isAssignableFrom(SNSResponse.class)
                                && response.getStatusLine().getStatusCode()
                                        == HttpStatus.SC_ACCEPTED))) {
                    throw new HttpException(
                            ErrorCode.COMMON_ERROR_SYSTEM_ERROR,
                            "uri: "
                                    + request.getURI().toString()
                                    + ", statusCode: "
                                    + response.getStatusLine().getStatusCode()
                                    + ", entity: "
                                    + entity,
                            response.getStatusLine().getStatusCode());
                }

                if (clazz == String.class) {
                    return (S) entity;
                }

                S serializable = JsonUtil.decode(entity, clazz);

                if (serializable == null) {
                    log.warning(request.getURI().toString(), entity);
                    return null;
                }

                if (httpCallback != null) {
                    httpCallback.callback(serializable, args);
                }

                return serializable;
            }
        } catch (IOException e) {
            throw new HttpException(e);
        } catch (Exception e) {
            log.info("HttpManagerCatch", JsonUtil.encode(e));
            throw e;
        }
    }

    public S doGet(
            String uri,
            List<NameValuePair> nameValuePairs,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        HttpGet httpGet;

        try {
            URIBuilder uriBuilder = new URIBuilder(uri);

            if (!CollectionUtils.isEmpty(nameValuePairs)) {
                uriBuilder.setParameters(nameValuePairs);
            }

            httpGet = new HttpGet(uriBuilder.build());
            return doRequest(httpGet, headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(
                    getClass().getName(),
                    "uri: " + uri + ", params: " + JsonUtil.encode(nameValuePairs));
            throw e;
        }
    }

    public S doDelete(
            String uri,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try {
            return doRequest(new HttpDelete(uri), headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(getClass().getName(), "uri: " + uri + ", e = " + e);
            throw e;
        }
    }

    public S doPutJson(
            String uri,
            Map<String, Object> parameterMap,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try {
            HttpPut httpPut = new HttpPut(uri);
            httpPut.setEntity(
                    new StringEntity(JsonUtil.encode(parameterMap), ContentType.APPLICATION_JSON));
            return doRequest(httpPut, headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(
                    getClass().getName(),
                    "uri: " + uri + ", params: " + JsonUtil.encode(parameterMap) + ", e = " + e);
            throw e;
        }
    }

    public S doPostJson(
            String uri,
            Map<String, Object> parameterMap,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try {
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setEntity(
                    new StringEntity(JsonUtil.encode(parameterMap), ContentType.APPLICATION_JSON));
            return doRequest(httpPost, headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(
                    getClass().getName(),
                    "uri: " + uri + ", params: " + JsonUtil.encode(parameterMap) + ", e = " + e);
            throw e;
        }
    }

    public S doPostForm(
            String uri,
            List<NameValuePair> nameValuePairs,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try {
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setEntity(
                    new UrlEncodedFormEntity(nameValuePairs, CharsetUtil.UTF_8.getCharset()));
            return doRequest(httpPost, headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(
                    getClass().getName(),
                    "uri: " + uri + ", params: " + JsonUtil.encode(nameValuePairs));
            throw e;
        }
    }

    public S doPostJsonList(
            String uri,
            List<TreeMap<String, Object>> parameterMap,
            Map<String, String> headerMap,
            HttpCallback<S> httpCallback,
            Class<S> clazz,
            Object... args)
            throws Exception {
        try {
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setEntity(
                    new StringEntity(JsonUtil.encode(parameterMap), ContentType.APPLICATION_JSON));
            return doRequest(httpPost, headerMap, httpCallback, clazz, args);
        } catch (Exception e) {
            log.warning(
                    getClass().getName(),
                    "uri: " + uri + ", params: " + JsonUtil.encode(parameterMap) + ", e = " + e);
            throw e;
        }
    }
}
