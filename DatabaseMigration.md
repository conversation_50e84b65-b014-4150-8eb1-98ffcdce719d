## Mysql DB User Categories and Permissions

#### DEV & STG

| **User Name**    | **User Type**   | **Scope of Access**                | **Privilege**                    | **Responsibilities**                                                        |
|------------------|-----------------|------------------------------------|----------------------------------|-----------------------------------------------------------------------------|
| **master**       | Rds Super User  | Entire database system             | USAGE, rds_superuser_role        | Used by database administrators to manage and maintain the database system. |
| **point**        | Program User    | Specific application or service    | USAGE, ALL PRIVILEGES ON point.* | Used by applications to interact with the database for CRUD operations.     |
| **point_viewer** | Read-Only User  | Entire database or specific tables | USAGE, SELECT ON point.*         | Used by analysts or reporting tools to extract data without modifying it.   |
| **viewer**       | Read-Only User  | Entire database or specific tables | USAGE, SELECT ON point.*         | Used by analysts or reporting tools to extract data without modifying it.   |
| **editor**       | Write-Only User | Specific tables or databases       | USAGE, ALL PRIVILEGES ON point.* | Used by applications or processes that only need to insert or update data.  |

## Postgres DB User Categories and Permissions

#### DEV & STG

| **User Name**    | **User Type**   | **Scope of Access**                | **Schema Privilege Permissions** | **Table Privilege**         | **Responsibilities**                                                        |
|------------------|-----------------|------------------------------------|----------------------------------|-----------------------------|-----------------------------------------------------------------------------|
| **master**       | Super User      | Entire database system             | USAGE,CREATE,ALTER               | SELECT,INSERT,UPDATE,DELETE | Used by database administrators to manage and maintain the database system. |
| **point**        | Program User    | Specific application or service    | USAGE,CREATE                     | SELECT,INSERT,UPDATE,DELETE | Used by applications to interact with the database for CRUD operations.     |
| **point_viewer** | Read-Only User  | Entire database or specific tables | USAGE,CREATE                     |                             | Used by analysts or reporting tools to extract data without modifying it.   |
| **viewer**       | Read-Only User  | Entire database or specific tables | USAGE,CREATE                     |                             | Used by analysts or reporting tools to extract data without modifying it.   |
| **editor**       | Write-Only User | Specific tables or databases       | USAGE,CREATE                     |                             | Used by applications or processes that only need to insert or update data.  |

## Database Migration File Structure

#### AWS Aurora MySQL Migration Files

```bash
 docker/flyway-mysql/sql/
  ├── V1.0.1__create_point_ddl.sql    # DDL - init table
  ├── V1.0.2__insert_point_dml.sql     # DML - insert basic data
```

#### Postgres Migration Files

```bash
docker/flyway-postgres/sql
├── V1.0.1__pos_create_ddl.sql    # DDL - init table
```

#### Naming Convention:

- Version Prefix: V{version_number}__ (double underscore)
- DDL Files: Contain CREATE/ALTER/DROP statements
- DML Files: Contain INSERT/UPDATE/DELETE/MERGE statements
- Descriptive Suffix: Clear purpose description

## Database Update Procedure Manuala

### 0. Operator Permissions and Prerequisites

#### Required Permissions

- SSH access to bastion host
- sudo docker privileges on bastion host
- Target database credentials `editor account password`

#### System Requirements

- Docker installed on bastion host
- File transfer tool between local workstation and bastion host (e.g. scp/sftp)
- Verify connectivity to RDS endpoint
- Access the bastion host in the **production environment** using ESS.

#### Risk Notice

❗ Always test SQL scripts in non-production environment first  
❗ Recommend taking database backup before execution  
❗ Verify MD5 checksums when transferring files between Windows/Linux (see troubleshooting section)

### 1. Get SQL File

#### Method 1: Upload SQL File to Bastion Host

```bash
# From local workstation (example)
scp /path/to/your_script.sql <bastion_username>@<bastion_IP>:<sql-filename>
# File verification (Linux)
ssh <bastion_username>@<bastion_IP> "md5sum <sql-filename>"
```

##### *Troubleshooting: MD5 Checksum Mismatches*

> Solutions:
> ```bash
> #Windows
> dos2unix <sql-filename> && md5sum <sql-filename>
> #Linux
> md5sum <sql-filename>
> ```

#### Method 2: login bastion host and get sql file from github

```bash
git clone https://github.com/backseat-inc/bs-point-server.git
```

### 2. Execute Database Update

#### AWS Aurora Mysql

```bash
ssh <bastion_username>@<bastion_IP>

# Execute update command (will prompt for DB password)
sudo docker run -it --rm -v <sql-filename>:<docker-sql-filename> mysql:8.0 \
sh -c "mysql -h <hostname> -u <username> -p -D <database-name> -vvv < <docker-sql-filename>"
# Monitor output carefully
# -vvv parameter enables verbose execution logging
# "Query OK" indicates success, "ERROR" requires investigation
```

###### Example for AWS Aurora Mysql

```bash
$ sudo docker run -it --rm   -v /home/<USER>/test/script.sql:/tmp/script.sql  --user root   mysql:8.0  sh -c "mysql -h point.cluster-cfwww0igyzpo.ap-northeast-1.rds.amazonaws.com -u editor -p -D point-test -vvv < /tmp/script.sql"
Enter password:
--------------
SET NAMES utf8mb4
--------------
Query OK, 0 rows affected (0.00 sec)
...
--------------
SET FOREIGN_KEY_CHECKS = 1
--------------
Query OK, 0 rows affected (0.00 sec)
Bye
```

#### Postgres

```bash
ssh <bastion_username>@<bastion_IP>
# Execute update command (will prompt for DB password)
sudo docker run -it --rm -v <psql-sql-filename>:<docker-psql-sql-filename>  postgres \
sh -c "psql -p 5439 -h  <hostname>  -U <username>  <database-name> < <docker-psql-sql-filename>"

# Monitor output carefully
```

###### Example for Postgres

Omit, see Example for AWS Aurora Mysql section

### 3. Verify Execution Results

#### AWS Aurora MySQ

```bash
#Interactive Verification
sudo docker run -it --rm mysql:8.0 \
mysql -h <hostname> -u <username> -p -D <database-name> 

# Execute verification queries in MySQL CLI
# Examples: Check count/specific data
SELECT COUNT(*) FROM <critical_table>;
```

#### postgres

```bash
#Interactive Verification
sudo docker run -it --rm  postgres \
psql -p 5439 -h  <hostname>  -U <username>  <database-name>

# Execute verification queries in MySQL CLI
# Examples: Check point user privilege
SELECT has_schema_privilege('point', 'public', 'USAGE') AS can_access_public;
```

### 4. Remove SQL files from bastion post-operation

```bash
rm <sql-filename>
```