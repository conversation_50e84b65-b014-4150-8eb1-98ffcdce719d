#!/bin/bash

function show_help() {
  cat <<EOF
usage: $0 <options> <environment>

options:
 -h or --help ... show help
 -b or --build ... do build
 -p or --push  ... do docker push after build
 -k or --restart-deployment ... restart k8s deployment
 -v <version> ... specify docker image version

example:

build image, docker push, and rolling update to dev:
./deployment/scripts/build.sh dev -b -p -k
EOF
}

function build() {
  echo
  echo "build"

  cd $BUILD_DIR
  ENVIRONMENT=$ENVIRONMENT \
    BUILD_DOCKER_IMAGE_NAME=$BUILD_DOCKER_IMAGE_NAME \
    BUILD_DOCKER_IMAGE_VERSION=$BUILD_DOCKER_IMAGE_VERSION \
    gradle bootBuildImage -x test --stacktrace
}

function docker_push() {
  URI=$BUILD_DOCKER_IMAGE_NAME:$BUILD_DOCKER_IMAGE_VERSION
  echo
  echo "docker push $URI"
  docker push $URI
}

function k8s_restart() {
  echo
  echo "Restart k8s deployment: $K8S_DEPLOYMENT"
  kubectl rollout restart deployment $K8S_DEPLOYMENT
}

BUILD_DIR=$(pwd)
ENV_DIR=$(cd $BUILD_DIR/deploy/env; pwd)

ENVIRONMENT=
BUILD_DOCKER_IMAGE_VERSION_ARG=

DO_BUILD=
DO_DOCKER_PUSH=
DO_K8S_RESTART=
DO_SHOW_HELP=

# parse args
while true; do
  if [ -z $1 ]; then
    break
  fi
  case $1 in
    -h|--help) DO_SHOW_HELP=1 ;;
    -b|--build) DO_BUILD=1 ;;
    -p|--push) DO_DOCKER_PUSH=1 ;;
    -k|--restart-deployment) DO_K8S_RESTART=1 ;;
    "-v") shift; BUILD_DOCKER_IMAGE_VERSION_ARG=$1 ;;
    *)
      if [ -z $ENVIRONMENT ]; then
        ENVIRONMENT=$1
      fi
      ;;
  esac
  shift
done

# validate
if [[ -z $ENVIRONMENT ]] || [[ "$DO_SHOW_HELP" = 1 ]]; then
  show_help
  exit 1
fi

# load env file
envfile=$ENV_DIR/$ENVIRONMENT

if [ ! -f $envfile ]; then
  echo "env file not found: $envfile"
  exit 1
fi

source $envfile

# update env
if [ ! -z $BUILD_DOCKER_IMAGE_VERSION_ARG ]; then
  BUILD_DOCKER_IMAGE_VERSION=$BUILD_DOCKER_IMAGE_VERSION_ARG
fi

# build image
if [ "$DO_BUILD" = 1 ]; then
  build

  if [ $? != 0 ]; then
    echo "build failed. aborted."
    exit 1
  fi
fi

# docker push
if [ "$DO_DOCKER_PUSH" = "1" ]; then
  docker_push

  if [ $? != 0 ]; then
    echo "build failed. aborted."
    exit 1
  fi
fi

# restart k8s deployment
if [ "$DO_K8S_RESTART" = "1" ]; then
  k8s_restart 

  if [ $? != 0 ]; then
    echo "build failed. aborted."
    exit 1
  fi
fi
