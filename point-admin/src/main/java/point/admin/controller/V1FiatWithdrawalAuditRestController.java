package point.admin.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.component.CsvDownloadManager;
import point.common.component.DataSourceManager;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/fiat-withdrawal-audit")
public class V1FiatWithdrawalAuditRestController extends ExchangeAdminController {

    private final DataSourceManager dataSourceManager;
    private final CsvDownloadManager<ReportFiatWithdrawalAuditStatement> downloadManager;

    @GetMapping("/download")
    public void download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        String sql =
                """
            SELECT
                fwa.fiat_withdrawal_id,
                fwa.created_at as modified_at,
                fwa.created_by as modified_by,
                fwa.status,
                fw.amount,
                fw.created_at,
                fw.updated_at as last_modified_at,
                fw.fiat_withdrawal_status as current_status
            FROM
                fiat_withdrawal_audit fwa
            LEFT JOIN
              fiat_withdrawal fw ON fw.id = fwa.fiat_withdrawal_id
            WHERE 1 = 1
                and fwa.created_at >= :from
                and fwa.created_at <= :to
        """;

        // dynamic sql
        if (userId != null) sql += " and fw.user_id = :uid ";

        sql += " ORDER BY fwa.fiat_withdrawal_id, fwa.created_at ";
        Query query =
                dataSourceManager
                        .getMasterEntityManagerFactory()
                        .createEntityManager()
                        .createNativeQuery(sql);

        // parameter for dynamic sql
        if (userId != null) query.setParameter("uid", userId);

        DateTimeFormatter formatter =
                DateTimeFormatter.ofPattern("yyyy-MM-dd")
                        .withLocale(Locale.JAPAN)
                        .withZone(ZoneId.systemDefault());
        query.setParameter(
                "from",
                dateFrom != null ? formatter.format(Instant.ofEpochMilli(dateFrom)) : "2000-01-01");
        query.setParameter(
                "to",
                dateTo != null
                        ? formatter.format(Instant.ofEpochMilli(dateTo).plus(1, ChronoUnit.DAYS))
                        : "2100-01-01");

        @SuppressWarnings("unchecked")
        List<Object[]> list = query.getResultList();

        List<ReportFiatWithdrawalAuditStatement> statements = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ReportFiatWithdrawalAuditStatement statement = new ReportFiatWithdrawalAuditStatement();
            Object[] rec = list.get(i);
            statement.setWithdrawalId(String.valueOf(rec[0]));
            statement.setModifiedAt(String.valueOf(rec[1]));
            statement.setModifiedBy(String.valueOf(rec[2]));
            statement.setStatus(String.valueOf(rec[3]));
            statement.setAmount(String.valueOf(rec[4]));
            statement.setCreatedAt(String.valueOf(rec[5]));
            statement.setLastModifiedAt(String.valueOf(rec[6]));
            statement.setCurrentStatus(String.valueOf(rec[7]));
            statements.add(statement);
        }

        downloadManager.download(response, statements, "fiat_withdrawal_audit", true);
    }

    @JsonPropertyOrder({"リクエストID", "更新日", "更新者", "ステータス", "数量", "登録日", "最終更新日", "現在ステータス"})
    class ReportFiatWithdrawalAuditStatement {

        @Getter
        @Setter
        @JsonProperty("リクエストID")
        private String withdrawalId;

        @Getter
        @Setter
        @JsonProperty("更新日")
        private String modifiedAt;

        @Getter
        @Setter
        @JsonProperty("更新者")
        private String modifiedBy;

        @Getter
        @Setter
        @JsonProperty("ステータス")
        private String status;

        @Getter
        @Setter
        @JsonProperty("数量")
        private String amount;

        @Getter
        @Setter
        @JsonProperty("登録日")
        private String createdAt;

        @Getter
        @Setter
        @JsonProperty("最終更新日")
        private String lastModifiedAt;

        @Getter
        @Setter
        @JsonProperty("現在ステータス")
        private String currentStatus;
    }
}
