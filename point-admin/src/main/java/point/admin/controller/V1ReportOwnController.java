package point.admin.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.response.ReportOwnStatement;
import point.common.component.CsvDownloadManager;
import point.common.constant.*;
import point.common.constant.Currency;
import point.common.entity.*;
import point.common.service.*;
import point.common.util.DateUnit;
import point.common.util.FormatUtil;
import point.common.util.FormatUtil.FormatPattern;
import point.pos.entity.PosTrade;
import point.pos.service.PosTradeService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/report-own")
public class V1ReportOwnController {

    private final SymbolService symbolService;
    private final AssetSummaryService assetSummaryService;
    private final UserService userService;
    private final FiatDepositService fiatDepositService;
    private final FiatWithdrawalService fiatWithdrawalService;
    private final CsvDownloadManager<ReportOwnStatement> downloadManager;
    private final PosTradeService posTradeService;

    @GetMapping()
    @PreAuthorize("@auth.check('report-own')")
    public String download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date defulStartTime = formatter.parse("2000-01-01");

        // パラメータ日時がなければ20000101を基本日時とする
        final var dateStart = dateFrom == null ? defulStartTime : new Date(dateFrom);

        // 日時からdateEndを作成する
        final var dateEnd =
                dateTo == null
                        ? new Date(new Date().getTime() + DateUnit.DAY.getMillis())
                        : new Date(dateTo + DateUnit.DAY.getMillis());

        // 日時からfromを作成する
        final var from = DateUnit.createFromTo(dateStart.getTime()).getLeft();

        // 日時からfromToを作成する
        final var fromTo = DateUnit.createFromTo(dateEnd.getTime()).getLeft();

        // DBからデータを取得する
        final var readDto = read(from, fromTo);

        // 出力DTOを生成する
        final var writeDtos = process(readDto);

        // ダウンロード情報を返却する
        String csvDateFrom =
                dateFrom != null
                        ? FormatUtil.format(new Date(dateFrom), FormatPattern.YYYYMMDD)
                        : "20000101";
        String csvDateTo =
                dateTo != null
                        ? FormatUtil.format(new Date(dateTo), FormatPattern.YYYYMMDD)
                        : FormatUtil.format(new Date(), FormatPattern.YYYYMMDD);
        downloadManager.download(
                response, writeDtos, "自己勘定元帳" + "_" + csvDateFrom + "_" + csvDateTo, true);

        return null;
    }

    // DBからデータを取得する
    protected ReadData read(Date dateFrom, Date dateTo) {
        // Symbolを全て取得する
        final var symbols = symbolService.findAll();
        // 有効なユーザを全て取得する
        final var users =
                userService.findAll().stream().filter(user -> user.getUserInfo() != null).toList();
        // 自社口座を全て取得する
        final var systemUsers =
                userService.findByInsideAccountFlg(true).stream()
                        .filter(user -> user.getUserInfo() != null)
                        .toList();
        var sysytemAssetSummaries = new ArrayList<AssetSummary>();
        // 日付から資産集計を取得する
        Date asDateFrom = new Date(dateFrom.getTime() - DateUnit.DAY.getMillis());
        Date asDateTo = new Date(dateTo.getTime() - DateUnit.DAY.getMillis());
        for (var systemUser : systemUsers) {
            final var assetSummaries =
                    assetSummaryService.findByCondition(
                            systemUser.getId(), null, asDateFrom, asDateTo);
            sysytemAssetSummaries.addAll(assetSummaries);
        }

        List<Symbol> posSymbols =
                symbols.stream().filter(x -> x.getTradeType() == TradeType.INVEST).toList();
        List<PosTrade> posTrades = new ArrayList<>();
        posSymbols.forEach(
                symbol -> {
                    // テーブル「pos_trade」からデータを取得する
                    List<PosTrade> posTradesFromSymbol =
                            posTradeService.findAllByCondition(
                                    symbol.getId(),
                                    null,
                                    null,
                                    null,
                                    null,
                                    dateFrom.getTime(),
                                    dateTo.getTime(),
                                    null,
                                    null,
                                    null);

                    if (posTrades != null) {
                        posTrades.addAll(posTradesFromSymbol);
                    }

                    List<PosTrade> posTradesFromHistory =
                            posTradeService.findFromPosTradeHistory(
                                    symbol,
                                    null,
                                    null,
                                    null,
                                    null,
                                    dateFrom,
                                    dateTo,
                                    0,
                                    Integer.MAX_VALUE);
                    if (posTrades != null) {
                        posTrades.addAll(posTradesFromHistory);
                    }
                });

        // 日付から日本円入金を取得する(完了のみ)
        final var fiatDeposits =
                fiatDepositService.findByCondition(
                        null,
                        null,
                        null,
                        dateFrom.getTime(),
                        dateTo.getTime(),
                        List.of(FiatDepositStatus.DONE),
                        0,
                        Integer.MAX_VALUE);
        // 日付から日本円出金を取得する(完了のみ)
        final var fiatWithdrawals =
                fiatWithdrawalService.findByCondition(
                        null,
                        null,
                        null,
                        dateFrom.getTime(),
                        dateTo.getTime(),
                        List.of(FiatWithdrawalStatus.DONE),
                        0,
                        Integer.MAX_VALUE);

        // 取得データを生成する
        return new ReadData(
                symbols,
                users,
                sysytemAssetSummaries,
                posTrades,
                fiatDeposits,
                fiatWithdrawals,
                systemUsers);
    }

    // 取得データを出力形式に整形する
    protected List<ReportOwnStatement> process(ReadData readData) {
        // symbolをMAP化する
        final var symbolMap =
                readData.symbols.stream()
                        .collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // ユーザをMAP化する
        final var userMap =
                readData.users.stream().collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // 運用口座をMAP化する
        final var systemUserMap =
                readData.systemUsers.stream()
                        .collect(Collectors.toMap(AbstractEntity::getId, it -> it));
        // 約定(BaseCurrency)をWrapする(販売所)
        final var posTradeBaseWrappers =
                readData.posTrades.stream()
                        .map(
                                it ->
                                        new ReportDtoWrapper(
                                                symbolMap,
                                                systemUserMap.get(it.getUserId()),
                                                createPosTrade(it, readData.systemUsers),
                                                null,
                                                null,
                                                null));
        // 約定(QuoteCurrency)をWrapする(販売所)
        final var posTradeQuoteWrappers =
                readData.posTrades.stream()
                        .map(
                                it ->
                                        new ReportDtoWrapper(
                                                symbolMap,
                                                systemUserMap.get(it.getUserId()),
                                                null,
                                                createPosTrade(it, readData.systemUsers),
                                                null,
                                                null));

        // 日本円入金をWrapする
        final var fiatDepositWrappers =
                (readData.fiatDeposits == null
                                ? new ArrayList<FiatDeposit>()
                                : readData.fiatDeposits)
                        .stream()
                                .map(
                                        it ->
                                                new ReportDtoWrapper(
                                                        symbolMap,
                                                        systemUserMap.get(it.getUserId()),
                                                        null,
                                                        null,
                                                        it,
                                                        null));
        // 日本円出金をWrapする
        final var fiatWithdrawalWrappers =
                readData.fiatWithdrawals.stream()
                        .map(
                                it ->
                                        new ReportDtoWrapper(
                                                symbolMap,
                                                systemUserMap.get(it.getUserId()),
                                                null,
                                                null,
                                                null,
                                                it));

        // WrapされたアイテムをMAP化する
        final var wrapperMap =
                Stream.of(
                                posTradeBaseWrappers,
                                posTradeQuoteWrappers,
                                fiatDepositWrappers,
                                fiatWithdrawalWrappers)
                        .flatMap(Function.identity())
                        .collect(Collectors.groupingBy(ReportDtoWrapper::getAssetKey));
        // 資産合計をMAP化する
        final var assetSummaryMap =
                readData.sysytemAssetSummaries.stream()
                        .sorted(Comparator.comparing(AssetSummary::getTargetAt))
                        .collect(
                                Collectors.collectingAndThen(
                                        Collectors.toCollection(
                                                () ->
                                                        new TreeSet<>(
                                                                Comparator.comparing(
                                                                        o ->
                                                                                o.getUserId()
                                                                                        + ";"
                                                                                        + o
                                                                                                .getCurrency()))),
                                        ArrayList::new))
                        .stream()
                        .collect(Collectors.toMap(AssetSummary::getAssetKey, it -> it));
        wrapperMap
                .entrySet()
                .forEach(
                        e -> {
                            if (ObjectUtils.isEmpty(assetSummaryMap.get(e.getKey()))) {
                                AssetSummary assetSummary = new AssetSummary();
                                assetSummary.setCurrentAmount(BigDecimal.ZERO);
                                assetSummaryMap.put(e.getKey(), assetSummary);
                            }
                        });
        // Wrapされたアイテムを資産毎に出力DTOに変換する
        return assetSummaryMap.entrySet().stream()
                .flatMap(
                        e -> {
                            final var wrappers =
                                    wrapperMap.getOrDefault(e.getKey(), new ArrayList<>()).stream()
                                            .sorted(Comparator.comparing(ReportDtoWrapper::getDate))
                                            .toList();
                            var balance = e.getValue().getCurrentAmount();
                            final var writeDtos = new ArrayList<ReportOwnStatement>();
                            for (var wrapper : wrappers) {
                                final var writeDto =
                                        wrapper.toWriteDto(balance, userMap, systemUserMap);
                                if (writeDto != null) {
                                    balance = writeDto.balanceBigDecimal();
                                    writeDtos.add(writeDto);
                                }
                            }
                            return writeDtos.stream();
                        })
                .sorted(
                        Comparator.comparing(ReportOwnStatement::tradeType) // 取引所,販売所順番
                                .thenComparing(ReportOwnStatement::orderUserId) // ユーザIDの昇順
                                .thenComparing(ReportOwnStatement::date)
                                .thenComparing(ReportOwnStatement::order)) // 作成日時の昇順
                .toList();
    }

    public record ReadData(
            List<Symbol> symbols,
            List<User> users,
            List<AssetSummary> sysytemAssetSummaries,
            List<PosTrade> posTrades,
            List<FiatDeposit> fiatDeposits,
            List<FiatWithdrawal> fiatWithdrawals,
            List<User> systemUsers) {}

    // どれか1つだけ保持している
    public record ReportDtoWrapper(
            Map<Long, Symbol> symbolMap,
            User user,
            PosTrade posTradeBase,
            PosTrade posTradeQuote,
            FiatDeposit fiatDeposit,
            FiatWithdrawal fiatWithdrawal) {

        public AssetKey getAssetKey() {
            if (posTradeBase != null) {
                final var currencyPair =
                        symbolMap.get(posTradeBase.getSymbolId()).getCurrencyPair();
                return new AssetKey(posTradeBase.getUserId(), currencyPair.getBaseCurrency());
            }
            if (posTradeQuote != null) {
                final var currencyPair =
                        symbolMap.get(posTradeQuote.getSymbolId()).getCurrencyPair();
                return new AssetKey(posTradeQuote.getUserId(), currencyPair.getQuoteCurrency());
            }
            if (fiatDeposit != null) {
                return new AssetKey(fiatDeposit.getUserId(), Currency.JPY);
            }
            if (fiatWithdrawal != null) {
                return new AssetKey(fiatWithdrawal.getUserId(), Currency.JPY);
            }
            throw new RuntimeException("assetKey not found");
        }

        private Date getDate() {
            if (posTradeBase != null) {
                return posTradeBase.getCreatedAt();
            }
            if (posTradeQuote != null) {
                return posTradeQuote.getCreatedAt();
            }
            if (fiatDeposit != null) {
                return fiatDeposit.getUpdatedAt();
            }
            if (fiatWithdrawal != null) {
                return fiatWithdrawal.getUpdatedAt();
            }
            throw new RuntimeException("date not found");
        }

        private ReportOwnStatement toWriteDto(
                BigDecimal balance, Map<Long, User> userMap, Map<Long, User> systemUserMap) {
            if (posTradeBase != null) {
                if (systemUserMap.get(posTradeBase.getUserId()) != null) {
                    return ReportOwnStatement.createBase(
                            symbolMap, user, posTradeBase, balance, userMap);
                }
                return null;
            }
            if (posTradeQuote != null) {
                if (systemUserMap.get(posTradeQuote.getUserId()) != null) {
                    return ReportOwnStatement.createQuote(
                            symbolMap, user, posTradeQuote, balance, userMap);
                }
                return null;
            }
            if (fiatDeposit != null) {
                if (systemUserMap.get(fiatDeposit.getUserId()) != null) {
                    return ReportOwnStatement.create(user, fiatDeposit, balance);
                }
                return null;
            }
            if (fiatWithdrawal != null) {
                if (systemUserMap.get(fiatWithdrawal.getUserId()) != null) {
                    return ReportOwnStatement.create(user, fiatWithdrawal, balance);
                }
                return null;
            }
            throw new RuntimeException("writeDto not found");
        }
    }

    public PosTrade createPosTrade(PosTrade trade, List<User> systemUsers) {
        PosTrade posTradeBaseTemp = new PosTrade();
        BeanUtils.copyProperties(trade, posTradeBaseTemp);
        // 販売所の取引の場合、運用口座と顧客の取引ではないため、「ー」で表示する。
        // 後で集計を取得するための一時的に運用口座を設定する。
        posTradeBaseTemp.setUserId(Long.valueOf(systemUsers.get(0).getId()));
        return posTradeBaseTemp;
    }
}
