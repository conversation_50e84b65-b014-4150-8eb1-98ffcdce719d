package point.admin.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.ViewVariables;
import point.common.entity.GmoDeposit;
import point.common.model.response.PageData;
import point.common.service.GmoDepositService;
import point.common.util.DateUnit;

@RestController
@RequestMapping("/admin/v1/gmo-deposit")
@RequiredArgsConstructor
public class V1GmoDepositRestController extends ExchangeAdminController {

    private final GmoDepositService gmoDepositService;

    @GetMapping
    @PreAuthorize("@auth.check('gmo-deposit')")
    public ResponseEntity<PageData<GmoDeposit>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "noFiatDepositId", required = false) Boolean noFiatDepositId,
            @RequestParam(value = "fiatDepositId", required = false) Long fiatDepositId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;
        return ResponseEntity.ok(
                gmoDepositService.findByConditionPageData(
                        noFiatDepositId, fiatDepositId, dateFrom, dateTo, number, size));
    }
}
