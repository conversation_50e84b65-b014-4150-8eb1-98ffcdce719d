package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.common.constant.UserIdType;
import point.common.constant.ViewVariables;
import point.common.model.response.PageData;
import point.common.model.response.UserPowerBalancePageData;
import point.common.service.ChoicePowerService;
import point.common.service.ChoicePowerUserRelService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/choice/power")
public class V1ChoicePowerController extends ExchangeAdminController {

    public final ChoicePowerService choicePowerService;

    public final ChoicePowerUserRelService choicePowerUserRelService;

    @GetMapping
    @PreAuthorize("@auth.check('choice-power-list')")
    public ResponseEntity<PageData<UserPowerBalancePageData>> get(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "partnerMemberId", required = false) String partnerMemberId,
            @RequestParam(value = "idType", required = false) UserIdType idType,
            @RequestParam(value = "powerAmountFrom", required = false) Integer powerAmountFrom,
            @RequestParam(value = "powerAmountTo", required = false) Integer powerAmountTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size) {
        PageData<UserPowerBalancePageData> pageData =
                choicePowerUserRelService.findByCondition(
                        userId,
                        partnerMemberId,
                        idType,
                        powerAmountFrom,
                        powerAmountTo,
                        number,
                        size);
        return ResponseEntity.ok(pageData);
    }
}
