package point.admin.controller;

import io.micrometer.core.instrument.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.Serializable;
import java.util.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.config.GlobalAuthenticationConfig;
import point.admin.entity.AdminUser;
import point.admin.entity.AdminUserAuthority;
import point.admin.model.request.AdminUserPostForm;
import point.admin.model.request.AdminUserPutForm;
import point.admin.model.request.AdminUserPutPasswordByOtherForm;
import point.admin.model.request.AdminUserPutPasswordForm;
import point.admin.service.AdminUserAuthorityService;
import point.admin.service.AdminUserLoginAttemptService;
import point.admin.service.AdminUserService;
import point.common.constant.ErrorCode;
import point.common.constant.ViewVariables;
import point.common.exception.CustomException;
import point.common.util.BindingResultUtil;
import springfox.documentation.annotations.ApiIgnore;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/admin-user")
@Api(tags = "admin user management")
public class V1AdminUserRestController extends ExchangeAdminController {

    private final AdminUserAuthorityService adminUserAuthorityService;

    private final AdminUserService adminUserService;

    private final GlobalAuthenticationConfig globalAuthenticationConfig;

    private final SessionRegistry sessionRegistry;

    private final AdminUserLoginAttemptService adminUserLoginAttemptService;

    @GetMapping("/token")
    public ResponseEntity<Object> get() {
        return ResponseEntity.ok().build();
    }

    @GetMapping
    @PreAuthorize("@auth.check('admin-users')")
    @ApiOperation(value = "search admin account info")
    public ResponseEntity<Serializable> get(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        if (id != null) {
            return ResponseEntity.ok(adminUserService.findOne(id));
        } else {
            return ResponseEntity.ok(adminUserService.findAll(number, size));
        }
    }

    @PostMapping("/register")
    @ApiOperation(value = "register admin account info")
    @PreAuthorize("@auth.check('admin-user-regist')")
    public ResponseEntity<Object> postRegister(
            @Valid @RequestBody AdminUserPostForm form, BindingResult result)
            throws CustomException {

        if (adminUserService.findByEmail(form.getEmail().trim()) != null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_SAME_ONE_EXIST);
        }

        if (StringUtils.isEmpty(form.getAuthority())) {
            throw new CustomException(
                    ErrorCode.REQUEST_ERROR_ADMIN_USER_REGISTER_AUTHORITY_NOT_FOUND);
        }

        // validation
        BindingResultUtil bindingResultUtil = new BindingResultUtil();
        if (bindingResultUtil.hasErrors(result)) {
            Map<String, Object> errorMessage = new HashMap<>();
            errorMessage.put("data", bindingResultUtil.getErrorList());
            errorMessage.put("code", ErrorCode.REQUEST_ERROR_PASSWORD_PATTERN.getCode());
            return ResponseEntity.badRequest().body(errorMessage);
        }

        AdminUser newAdminUser =
                new AdminUser(
                        form.getEmail(),
                        globalAuthenticationConfig.getBcryptHashpw(form.getPassword()));

        adminUserService.save(newAdminUser);

        AdminUserAuthority adminUserAuthority = new AdminUserAuthority();
        adminUserAuthority.setAdminUserId(newAdminUser.getId());
        adminUserAuthority.setAuthority(form.getAuthority());
        adminUserAuthorityService.save(adminUserAuthority);

        return ResponseEntity.ok(newAdminUser);
    }

    @PutMapping
    @ApiOperation(value = "update admin details info")
    public ResponseEntity<Object> put(@Valid @RequestBody AdminUserPutForm form)
            throws CustomException {

        AdminUser checeAdminUser = adminUserService.findByEmail(form.getEmail().trim());
        if (Objects.nonNull(checeAdminUser) && !checeAdminUser.getId().equals(form.getId())) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_SAME_ONE_EXIST);
        }
        AdminUser targetAdminUser = adminUserService.findOne(form.getId());
        adminUserService.save(targetAdminUser.setParams(form));
        if (form.isAccountNonLocked()) {
            adminUserLoginAttemptService.clear(targetAdminUser.getId());
        }
        expireAdminUser(sessionRegistry, targetAdminUser);
        return ResponseEntity.ok(targetAdminUser);
    }

    @PutMapping("/password/update-other")
    @ApiOperation(value = "update other password")
    public ResponseEntity<Object> putPasswordByAnother(
            @ApiIgnore @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody AdminUserPutPasswordByOtherForm form,
            BindingResult result) {

        BindingResultUtil bindingResultUtil = new BindingResultUtil();
        if (bindingResultUtil.hasErrors(result)) {
            return ResponseEntity.badRequest().body(bindingResultUtil.getErrorList());
        }

        AdminUser targetAdminUser = adminUserService.findOne(form.getAdminUserId());
        targetAdminUser.setPassword(globalAuthenticationConfig.getBcryptHashpw(form.getPassword()));
        // password force change when next login
        targetAdminUser.setPasswordForceChange(true);
        adminUserService.save(targetAdminUser);
        expireAdminUser(sessionRegistry, targetAdminUser);
        return ResponseEntity.ok(targetAdminUser);
    }

    @PutMapping("/password/update-self")
    @ApiOperation(value = "update self password")
    public ResponseEntity<Object> updatePassword(
            @ApiIgnore @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody AdminUserPutPasswordForm form,
            BindingResult result) {
        BindingResultUtil bindingResultUtil = new BindingResultUtil();
        boolean hasError = bindingResultUtil.hasErrors(result);
        List<String> errorList = bindingResultUtil.getErrorList();
        if (errorList == null) {
            errorList = new ArrayList<String>();
        }

        AdminUser targetAdminUser = adminUserService.findOne(adminUser.getId());

        // input old password == adminUser.password ?
        if (!globalAuthenticationConfig.matchesBCryptPassword(
                form.getOldPassword(), targetAdminUser.getPassword())) {
            hasError = true;
            errorList.add(0, "OldPasswordが正しくありません。");
        }
        // new Password != email
        if (form.getNewPassword().equals(targetAdminUser.getEmail())) {
            hasError = true;
            errorList.add("NewPasswordがメールアドレスと同じです。");
        }
        if (hasError) {
            return ResponseEntity.badRequest().body(errorList);
        }
        targetAdminUser.setPassword(
                globalAuthenticationConfig.getBcryptHashpw(form.getNewPassword()));
        targetAdminUser.setPasswordForceChange(false);
        return ResponseEntity.ok(adminUserService.save(targetAdminUser));
    }
}
