package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.common.constant.CurrencyPair;
import point.common.constant.ErrorCode;
import point.common.constant.TradeType;
import point.common.entity.CurrencyPairConfig;
import point.common.exception.CustomException;
import point.common.model.request.CurrencyPairConfigUpdateForm;
import point.common.service.CurrencyPairConfigService;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/currency-pair-config")
public class V1CurrencyPairConfigRestController extends ExchangeAdminController {
    private final CurrencyPairConfigService currencyPairConfigService;

    @GetMapping
    @PreAuthorize("@auth.check('currency-pair-config')")
    public ResponseEntity<List<CurrencyPairConfig>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "tradeType", required = false) TradeType tradeType,
            @RequestParam(value = "currencyPair", required = false) CurrencyPair currencyPair)
            throws CustomException {
        if (id != null) {
            List<CurrencyPairConfig> currencyPairConfigs = new ArrayList<>();
            CurrencyPairConfig found = currencyPairConfigService.findOne(id);
            if (found != null) {
                currencyPairConfigs.add(found);
            }
            return ResponseEntity.ok(currencyPairConfigs);
        } else {
            // adminは無効(enabled=false)の通貨ペアも参照可能とする
            return ResponseEntity.ok(
                    currencyPairConfigService.findAllByCondition(tradeType, currencyPair));
        }
    }

    @PutMapping
    @PreAuthorize("@auth.check('currency-pair-config')")
    public ResponseEntity<CurrencyPairConfig> update(
            @Valid @RequestBody CurrencyPairConfigUpdateForm form) throws CustomException {
        CurrencyPairConfig currencyPairConfig = currencyPairConfigService.findOne(form.getId());

        if (currencyPairConfig == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_API_INFO_IS_NULL);
        }

        currencyPairConfig.setMinOrderAmount(form.getMinOrderAmount());
        currencyPairConfig.setMaxOrderAmount(form.getMaxOrderAmount());
        currencyPairConfig.setMaxActiveOrderAmount(form.getMaxActiveOrderAmount());
        currencyPairConfig.setLimitPriceRangeRate(form.getLimitPriceRangeRate());
        currencyPairConfig.setMarketPriceRangeRate(form.getMarketPriceRangeRate());
        currencyPairConfig.setMarketAmountRangeRate(form.getMarketAmountRangeRate());
        currencyPairConfig.setMakerTradeFeePercent(form.getMakerTradeFeePercent());
        currencyPairConfig.setTakerTradeFeePercent(form.getTakerTradeFeePercent());
        currencyPairConfig.setSimpleMarketSpreadPercent(form.getSimpleMarketSpreadPercent());
        currencyPairConfig.setSimpleMarketFeePercent(form.getSimpleMarketFeePercent());
        currencyPairConfig.setTradable(form.isTradable());
        currencyPairConfig.setEnabled(form.isEnabled());
        currencyPairConfig.setCircuitBreakPercent(form.getCircuitBreakPercent());
        currencyPairConfig.setCircuitBreakCheckTimespan(form.getCircuitBreakCheckTimespan());
        currencyPairConfig.setCircuitBreakStopTimespan(form.getCircuitBreakStopTimespan());
        currencyPairConfig.setSpikePercent(form.getSpikePercent());
        currencyPairConfig.setSpikeMinutes(form.getSpikeMinutes());
        currencyPairConfig.setSpikeCount(form.getSpikeCount());
        currencyPairConfig.setWashTradingCheckSpanHours(form.getWashTradingCheckSpanHours());
        currencyPairConfig.setWashTradingPercentThreshold(form.getWashTradingPercentThreshold());
        currencyPairConfig.setHighValueTraderCheckSpanHours(
                form.getHighValueTraderCheckSpanHours());
        currencyPairConfig.setHighValueTraderCountThreshold(
                form.getHighValueTraderCountThreshold());
        currencyPairConfig.setPosSpreadPercent(form.getPosSpreadPercent());
        currencyPairConfig.setPosSlippagePercent(form.getPosSlippagePercent());
        currencyPairConfig.setHighValueTraderPosOrderLimitAmountThreshold(
                form.getHighValueTraderPosOrderLimitAmountThreshold());
        currencyPairConfig.setHighValueTraderPosTradeMarketAmountThreshold(
                form.getHighValueTraderPosTradeMarketAmountThreshold());
        return ResponseEntity.ok(currencyPairConfigService.save(currencyPairConfig));
    }

    @DeleteMapping
    public ResponseEntity<CurrencyPairConfig> delete(
            @AuthenticationPrincipal AdminUser adminUser, @RequestParam(value = "id") Long id)
            throws CustomException {

        CurrencyPairConfig currencyPairConfig = currencyPairConfigService.findOne(id);

        if (currencyPairConfig == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_API_INFO_IS_NULL);
        }

        currencyPairConfigService.delete(currencyPairConfig);
        return ResponseEntity.ok(currencyPairConfig);
    }
}
